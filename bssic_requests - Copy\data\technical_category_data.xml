<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Main Categories -->
        <record id="technical_category_hardware" model="bssic.technical.category">
            <field name="name">Hardware</field>
            <field name="code">hardware</field>
            <field name="sequence">10</field>
        </record>
        
        <record id="technical_category_hardware_repair" model="bssic.technical.category">
            <field name="name">Hardware Repair</field>
            <field name="code">hardware_repair</field>
            <field name="sequence">20</field>
            <field name="parent_id" ref="technical_category_hardware"/>
        </record>
        
        <record id="technical_category_new_hardware" model="bssic.technical.category">
            <field name="name">New Hardware Request</field>
            <field name="code">new_hardware</field>
            <field name="sequence">30</field>
            <field name="parent_id" ref="technical_category_hardware"/>
        </record>
        
        <record id="technical_category_software" model="bssic.technical.category">
            <field name="name">Software</field>
            <field name="code">software</field>
            <field name="sequence">40</field>
        </record>
        
        <record id="technical_category_software_installation" model="bssic.technical.category">
            <field name="name">Software Installation</field>
            <field name="code">software_installation</field>
            <field name="sequence">50</field>
            <field name="parent_id" ref="technical_category_software"/>
        </record>
        
        <record id="technical_category_software_issue" model="bssic.technical.category">
            <field name="name">Software Issue</field>
            <field name="code">software_issue</field>
            <field name="sequence">60</field>
            <field name="parent_id" ref="technical_category_software"/>
        </record>
        
        <record id="technical_category_network" model="bssic.technical.category">
            <field name="name">Network</field>
            <field name="code">network</field>
            <field name="sequence">70</field>
        </record>
        
        <record id="technical_category_network_access" model="bssic.technical.category">
            <field name="name">Network Access</field>
            <field name="code">network_access</field>
            <field name="sequence">80</field>
            <field name="parent_id" ref="technical_category_network"/>
        </record>
        
        <record id="technical_category_network_issue" model="bssic.technical.category">
            <field name="name">Network Issue</field>
            <field name="code">network_issue</field>
            <field name="sequence">90</field>
            <field name="parent_id" ref="technical_category_network"/>
        </record>
    </data>
</odoo>
