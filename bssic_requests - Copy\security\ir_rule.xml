<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- قاعدة عامة لجميع المستخدمين - تقييد الوصول للطلبات -->
    <record id="rule_bssic_request_global" model="ir.rule">
        <field name="name">BSIC Request: Global Rule</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
        <field name="global" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- قاعدة للمدراء المباشرين لرؤية طلبات فريقهم -->
    <record id="rule_bssic_request_direct_manager" model="ir.rule">
        <field name="name">BSIC Request: Direct Managers see their team's requests</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), ('employee_id.parent_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('bssic_requests.group_bssic_direct_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- قاعدة لمدراء التدقيق لرؤية الطلبات في مرحلة موافقة مدير التدقيق -->
    <record id="rule_bssic_request_audit_manager" model="ir.rule">
        <field name="name">BSIC Request: Audit Managers see requests in audit approval stage</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), ('state', '=', 'audit_manager')]</field>
        <field name="groups" eval="[(4, ref('bssic_requests.group_bssic_audit_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- قاعدة لمدراء تكنولوجيا المعلومات لرؤية الطلبات في مرحلة موافقة مدير تكنولوجيا المعلومات -->
    <record id="rule_bssic_request_it_manager" model="ir.rule">
        <field name="name">BSIC Request: IT Managers see requests in IT approval stage</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), '|', ('state', '=', 'it_manager'), ('state', '=', 'assigned')]</field>
        <field name="groups" eval="[(4, ref('bssic_requests.group_bssic_it_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <!-- قاعدة لموظفي تكنولوجيا المعلومات لرؤية الطلبات المسندة إليهم -->
    <record id="rule_bssic_request_it_staff" model="ir.rule">
        <field name="name">BSIC Request: IT Staff see requests assigned to them</field>
        <field name="model_id" ref="model_bssic_request"/>
        <field name="domain_force">['|', ('employee_id.user_id', '=', user.id), ('assigned_to.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('bssic_requests.group_bssic_it_staff'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>
</odoo>