<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Activity Log Tree View -->
    <record id="view_request_activity_log_tree" model="ir.ui.view">
        <field name="name">bssic.request.activity.log.tree</field>
        <field name="model">bssic.request.activity.log</field>
        <field name="arch" type="xml">
            <tree string="Activity Log" create="false" edit="false" delete="false">
                <field name="activity_date"/>
                <field name="activity_type"/>
                <field name="user_id"/>
                <field name="employee_id"/>
                <field name="old_state"/>
                <field name="new_state"/>
                <field name="notes"/>
            </tree>
        </field>
    </record>

    <!-- Activity Log Form View -->
    <record id="view_request_activity_log_form" model="ir.ui.view">
        <field name="name">bssic.request.activity.log.form</field>
        <field name="model">bssic.request.activity.log</field>
        <field name="arch" type="xml">
            <form string="Activity Log" create="false" edit="false" delete="false">
                <sheet>
                    <group>
                        <group>
                            <field name="activity_type" readonly="1"/>
                            <field name="activity_date" readonly="1"/>
                            <field name="user_id" readonly="1"/>
                            <field name="employee_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="old_state" readonly="1"/>
                            <field name="new_state" readonly="1"/>
                            <field name="assigned_to_id" readonly="1" attrs="{'invisible': [('assigned_to_id', '=', False)]}"/>
                        </group>
                    </group>
                    <group>
                        <field name="notes" readonly="1" attrs="{'invisible': [('notes', '=', False)]}"/>
                        <field name="rejection_reason" readonly="1" attrs="{'invisible': [('rejection_reason', '=', False)]}"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Activity Log Action -->
    <record id="action_request_activity_log" model="ir.actions.act_window">
        <field name="name">Activity Log</field>
        <field name="res_model">bssic.request.activity.log</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No activity logs found
            </p>
            <p>
                Activity logs will appear here when actions are performed on requests.
            </p>
        </field>
    </record>

    <!-- Activity Log Search View -->
    <record id="view_request_activity_log_search" model="ir.ui.view">
        <field name="name">bssic.request.activity.log.search</field>
        <field name="model">bssic.request.activity.log</field>
        <field name="arch" type="xml">
            <search string="Activity Log">
                <field name="activity_type"/>
                <field name="user_id"/>
                <field name="employee_id"/>
                <field name="activity_date"/>
                <field name="notes"/>
                
                <filter string="Today" name="today" 
                        domain="[('activity_date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), 
                                 ('activity_date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter string="This Week" name="this_week" 
                        domain="[('activity_date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')),
                                 ('activity_date', '&lt;=', (context_today() + datetime.timedelta(days=6-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" 
                        domain="[('activity_date', '&gt;=', context_today().strftime('%Y-%m-01')),
                                 ('activity_date', '&lt;=', (context_today().replace(day=1) + datetime.timedelta(days=32)).replace(day=1) - datetime.timedelta(days=1))]"/>
                
                <separator/>
                <filter string="Submitted" name="submitted" domain="[('activity_type', '=', 'submitted')]"/>
                <filter string="Approved" name="approved" domain="[('activity_type', 'in', ['direct_manager_approved', 'audit_manager_approved', 'it_manager_approved', 'hr_approved', 'warehouse_approved'])]"/>
                <filter string="Rejected" name="rejected" domain="[('activity_type', '=', 'rejected')]"/>
                <filter string="Completed" name="completed" domain="[('activity_type', '=', 'completed')]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Activity Type" name="group_activity_type" context="{'group_by': 'activity_type'}"/>
                    <filter string="User" name="group_user" context="{'group_by': 'user_id'}"/>
                    <filter string="Employee" name="group_employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Date" name="group_date" context="{'group_by': 'activity_date:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Activity Log Kanban View -->
    <record id="view_request_activity_log_kanban" model="ir.ui.view">
        <field name="name">bssic.request.activity.log.kanban</field>
        <field name="model">bssic.request.activity.log</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile" create="false" edit="false" delete="false">
                <field name="activity_type"/>
                <field name="activity_date"/>
                <field name="user_id"/>
                <field name="employee_id"/>
                <field name="notes"/>
                <field name="old_state"/>
                <field name="new_state"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="activity_type"/>
                                    </strong>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div>
                                        <i class="fa fa-user"/> <field name="user_id"/>
                                    </div>
                                    <div>
                                        <i class="fa fa-calendar"/> <field name="activity_date"/>
                                    </div>
                                    <div t-if="record.old_state.raw_value and record.new_state.raw_value">
                                        <i class="fa fa-arrow-right"/> 
                                        <field name="old_state"/> → <field name="new_state"/>
                                    </div>
                                    <div t-if="record.notes.raw_value">
                                        <i class="fa fa-comment"/> <field name="notes"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

</odoo>
