from odoo import models, fields, api, _

class ReceiptConfirmationWizard(models.TransientModel):
    _name = 'bssic.receipt.confirmation.wizard'
    _description = 'Receipt Confirmation Wizard'
    
    stationery_request_id = fields.Many2one('bssic.stationery.request', string='Stationery Request')
    receipt_notes = fields.Text('Receipt Notes')
    
    def action_confirm_receipt(self):
        self.ensure_one()
        if self.stationery_request_id:
            # Update receipt notes if provided
            vals = {}
            if self.receipt_notes:
                vals['receipt_notes'] = self.receipt_notes
                
            # Write values if any
            if vals:
                self.stationery_request_id.write(vals)
                
            # Call the confirm receipt method
            self.stationery_request_id.action_confirm_receipt()
            
        return {'type': 'ir.actions.act_window_close'}
