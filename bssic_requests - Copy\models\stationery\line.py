from odoo import models, fields, api, _
from odoo.exceptions import UserError


class StationeryRequestLine(models.Model):
    _name = 'bssic.stationery.request.line'
    _description = 'Stationery Request Line'

    request_id = fields.Many2one('bssic.stationery.request', string='Request', required=True, ondelete='cascade')
    item_id = fields.Many2one('bssic.stationery.item', string='Item', required=True)
    requested_quantity = fields.Float('Requested Quantity', default=1.0, required=True)
    stock_quantity = fields.Float('Stock Quantity', default=0.0)
    approved_quantity = fields.Float('Approved Quantity', default=0.0)
    can_edit_approved_quantity = fields.Boolean(compute='_compute_can_edit_approved_quantity')
    can_edit_stock_quantity = fields.Boolean(compute='_compute_can_edit_stock_quantity')
    notes = fields.Char('Notes')

    @api.constrains('approved_quantity', 'request_id.state')
    def _check_approved_quantity(self):
        """Ensure approved quantity is not negative when HR manager approves the request"""
        for record in self:
            if record.request_id.state == 'pending_receipt' and record.approved_quantity < 0:
                raise UserError(_('Approved quantity cannot be negative when approving the request.'))

    @api.depends('request_id.state')
    def _compute_can_edit_approved_quantity(self):
        """Check if the current user can edit the approved quantity"""
        is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
        for record in self:
            record.can_edit_approved_quantity = is_hr_manager and record.request_id.state == 'hr_approval'

    @api.depends('request_id.state')
    def _compute_can_edit_stock_quantity(self):
        """Check if the current user can edit the stock quantity"""
        is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')
        for record in self:
            record.can_edit_stock_quantity = is_warehouse_manager and record.request_id.state == 'warehouse_approval'

    def write(self, vals):
        """Override write to check permissions for approved_quantity and stock_quantity"""
        if 'approved_quantity' in vals:
            # Check if user is HR manager and request is in HR approval state
            is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
            if not is_hr_manager:
                # Remove approved_quantity from vals if user is not HR manager
                vals.pop('approved_quantity')
            elif self.request_id.state != 'hr_approval':
                # Remove approved_quantity from vals if request is not in HR approval state
                vals.pop('approved_quantity')

        if 'stock_quantity' in vals:
            # Check if user is Warehouse manager and request is in Warehouse approval state
            is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')
            if not is_warehouse_manager:
                # Remove stock_quantity from vals if user is not Warehouse manager
                vals.pop('stock_quantity')
            elif self.request_id.state != 'warehouse_approval':
                # Remove stock_quantity from vals if request is not in Warehouse approval state
                vals.pop('stock_quantity')

        return super(StationeryRequestLine, self).write(vals)
