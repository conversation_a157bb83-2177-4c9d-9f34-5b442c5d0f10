from odoo import models, fields, api, _

class RequestRejectWizard(models.TransientModel):
    _name = 'bssic.request.reject.wizard'
    _description = 'Reject Request Wizard'

    request_id = fields.Many2one('bssic.request', string='Request')
    stationery_request_id = fields.Many2one('bssic.stationery.request', string='Stationery Request')
    model = fields.Char('Model Name')
    rejection_reason = fields.Text('Rejection Reason', required=True)

    def action_confirm_reject(self):
        self.ensure_one()

        # Handle regular requests
        if self.request_id:
            # Log activity
            if self.request_id.id:
                self.env['bssic.request.activity.log'].create_activity_log(
                    'bssic.request', self.request_id.id, 'rejected',
                    notes=_('Request rejected'),
                    old_state=self.request_id.state, new_state='rejected',
                    rejection_reason=self.rejection_reason
                )

            self.request_id.write({
                'state': 'rejected',
                'rejection_reason': self.rejection_reason
            })
            # Notify employee
            if self.request_id.employee_id.user_id:
                self.request_id.message_post(
                    body=_('Your request has been rejected. Reason: %s') % self.rejection_reason,
                    partner_ids=[self.request_id.employee_id.user_id.partner_id.id]
                )

        # Handle stationery requests
        elif self.stationery_request_id:
            # Log activity
            if self.stationery_request_id.id:
                self.env['bssic.request.activity.log'].create_activity_log(
                    'bssic.stationery.request', self.stationery_request_id.id, 'rejected',
                    notes=_('Stationery request rejected'),
                    old_state=self.stationery_request_id.state, new_state='rejected',
                    rejection_reason=self.rejection_reason
                )

            # Record rejection information
            self.stationery_request_id.write({
                'state': 'rejected',
                'rejection_reason': self.rejection_reason,
                'rejection_user_id': self.env.user.id,
                'rejection_date': fields.Datetime.now()
            })
            # Notify employee
            if self.stationery_request_id.employee_id.user_id:
                self.stationery_request_id.message_post(
                    body=_('Your stationery request has been rejected. Reason: %s') % self.rejection_reason,
                    partner_ids=[self.stationery_request_id.employee_id.user_id.partner_id.id]
                )

        return {'type': 'ir.actions.act_window_close'}