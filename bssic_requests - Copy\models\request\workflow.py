from odoo import models, fields, api, _
from odoo.exceptions import UserError


class BSSICRequest(models.Model):
    _inherit = 'bssic.request'

    def action_submit(self):
        """Submit request for approval"""
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'submitted',
                notes=_('Request submitted for approval'),
                old_state='draft', new_state='direct_manager'
            )

        # First set state to submitted
        self.state = 'submitted'
        # Then move to direct manager approval
        self.state = 'direct_manager'
        # Notify direct manager
        direct_manager = self.employee_id.parent_id
        if direct_manager and direct_manager.user_id:
            self.message_subscribe(partner_ids=[direct_manager.user_id.partner_id.id])
            self.message_post(
                body=_('A new request has been submitted for your approval.'),
                partner_ids=[direct_manager.user_id.partner_id.id]
            )

    def action_approve_direct_manager(self):
        """Approve request by direct manager"""
        # Check if this is a technical request
        if self.is_technical or self.request_type_id.code == 'technical':
            # Log activity
            if self.id:
                self.env['bssic.request.activity.log'].create_activity_log(
                    'bssic.request', self.id, 'direct_manager_approved',
                    notes=_('Approved by Direct Manager (Technical Request - Skip Audit)'),
                    old_state='direct_manager', new_state='it_manager'
                )

            # Skip audit manager approval for technical requests
            self.state = 'it_manager'
            # Find IT manager and notify
            it_manager_group = self.env.ref('bssic_requests.group_bssic_it_manager')
            it_manager_users = it_manager_group.users
            partner_ids = it_manager_users.mapped('partner_id.id')
            if partner_ids:
                self.message_subscribe(partner_ids=partner_ids)
                self.message_post(
                    body=_('This technical request has been approved by the direct manager and requires your approval.'),
                    partner_ids=partner_ids
                )
        else:
            # Log activity
            if self.id:
                self.env['bssic.request.activity.log'].create_activity_log(
                    'bssic.request', self.id, 'direct_manager_approved',
                    notes=_('Approved by Direct Manager'),
                    old_state='direct_manager', new_state='audit_manager'
                )

            # Regular workflow for non-technical requests
            self.state = 'audit_manager'
            # Find audit manager and notify
            audit_group = self.env.ref('bssic_requests.group_bssic_audit_manager')
            audit_users = audit_group.users
            partner_ids = audit_users.mapped('partner_id.id')
            if partner_ids:
                self.message_subscribe(partner_ids=partner_ids)
                self.message_post(
                    body=_('This request has been approved by the direct manager and requires your approval.'),
                    partner_ids=partner_ids
                )

    def action_approve_audit_manager(self):
        """Approve request by audit manager"""
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'audit_manager_approved',
                notes=_('Approved by Audit Manager'),
                old_state='audit_manager', new_state='it_manager'
            )

        self.state = 'it_manager'
        # Find IT manager and notify
        it_manager_group = self.env.ref('bssic_requests.group_bssic_it_manager')
        it_manager_users = it_manager_group.users
        partner_ids = it_manager_users.mapped('partner_id.id')
        if partner_ids:
            self.message_subscribe(partner_ids=partner_ids)
            self.message_post(
                body=_('This request has been approved by the audit manager and requires your approval.'),
                partner_ids=partner_ids
            )

    def action_approve_it_manager(self):
        """Approve request by IT manager"""
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'it_manager_approved',
                notes=_('Approved by IT Manager'),
                old_state='it_manager', new_state='assigned'
            )

        self.state = 'assigned'

    def action_assign(self):
        """Assign request to IT staff"""
        if not self.assigned_to:
            raise UserError(_('Please select an IT staff member to assign this request to.'))

        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'assigned',
                notes=_('Assigned to IT Staff: %s') % self.assigned_to.name,
                old_state='assigned', new_state='in_progress',
                assigned_to_id=self.assigned_to.id
            )

        self.state = 'in_progress'
        # Notify assigned staff
        if self.assigned_to.user_id:
            self.message_subscribe(partner_ids=[self.assigned_to.user_id.partner_id.id])
            self.message_post(
                body=_('This request has been assigned to you for implementation.'),
                partner_ids=[self.assigned_to.user_id.partner_id.id]
            )

    def action_complete(self):
        """Complete the request"""
        if not self.completion_notes:
            raise UserError(_('Please add completion notes before marking as completed.'))

        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'completed',
                notes=_('Request completed. Notes: %s') % self.completion_notes,
                old_state='in_progress', new_state='completed'
            )

        self.state = 'completed'
        # Notify employee
        if self.employee_id.user_id:
            self.message_post(
                body=_('Your request has been completed. Notes: %s') % self.completion_notes,
                partner_ids=[self.employee_id.user_id.partner_id.id]
            )
