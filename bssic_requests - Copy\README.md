# BSIC Requests Management

This module provides a comprehensive system for managing various types of requests in BSIC with a multi-stage approval workflow.

## Features

- Multiple request types:
  - Password Reset
  - USB Storage Usage
  - Device Usage Extension

- Multi-stage approval workflow:
  1. Employee submits request
  2. Direct Manager approval
  3. Audit Manager approval
  4. IT Manager approval
  5. Assignment to IT Staff
  6. Implementation
  7. Completion

- Role-based permissions:
  - Employees can create and view their own requests
  - Direct Managers can approve their team's requests
  - Audit Managers can approve requests after Direct Managers
  - IT Managers can approve, assign, and manage all requests
  - IT Staff can implement assigned requests

- Notifications at each stage of the workflow
- Rejection with reason at any stage
- Integration with employee data using employee ID

## Configuration

1. Assign users to the appropriate security groups:
   - BSSIC Employee
   - BSSIC Direct Manager
   - BSSIC Audit Manager
   - BSSIC IT Manager
   - BSSIC IT Staff

2. Configure request types as needed

## Usage

1. Employee creates a request
2. Request goes through the approval workflow
3. IT staff implements the request
4. Employee receives notification of completion

## Technical Information

- The module integrates with the HR module to use employee data
- Uses the mail module for notifications and chatter
- Custom security groups and record rules for proper access control