<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Extension Request Form View -->
    <record id="view_bssic_extension_request_form" model="ir.ui.view">
        <field name="name">bssic.extension.request.form</field>
        <field name="model">bssic.request</field>
        <field name="inherit_id" ref="bssic_requests.view_bssic_request_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <!-- Override the header to customize buttons for extension requests -->
            <xpath expr="//header" position="replace">
                <header>
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"/>
                    <button name="action_approve_direct_manager" string="Approve (Direct Manager)" type="object" class="oe_highlight"
                            states="direct_manager" groups="bssic_requests.group_bssic_direct_manager"/>
                    <button name="action_approve_audit_manager" string="Approve (Audit Manager)" type="object" class="oe_highlight"
                            states="audit_manager" groups="bssic_requests.group_bssic_audit_manager"/>
                    <button name="action_approve_it_manager" string="Approve (IT Manager)" type="object" class="oe_highlight"
                            states="it_manager" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_assign" string="Assign to IT Staff" type="object" class="oe_highlight"
                            states="assigned" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_complete" string="Mark as Completed" type="object" class="oe_highlight"
                            states="in_progress" groups="bssic_requests.group_bssic_it_staff"/>
                    <button name="action_reject" string="Reject" type="object" class="btn-danger"
                            states="direct_manager,audit_manager,it_manager,assigned,in_progress"/>
                    <field name="is_technical" invisible="1"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,direct_manager,audit_manager,it_manager,assigned,in_progress,completed"/>
                </header>
            </xpath>
        </field>
    </record>
    
    <!-- Update the extension request action to use the new form view -->
    <record id="action_bssic_extension_request" model="ir.actions.act_window">
        <field name="name">Extension Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'extension')]</field>
        <field name="context">{'default_request_type_code': 'extension', 'form_view_ref': 'bssic_requests.view_bssic_extension_request_form', 'default_name': 'Extension Request'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new extension request
            </p>
        </field>
    </record>
</odoo>
