<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Main Menu -->
    <menuitem id="menu_bssic_requests_root" 
              name="Requests" 
              sequence="100" 
              groups="bssic_requests.group_bssic_employee"/>
    
    <!-- Requests Menu -->
    <record id="action_bssic_requests" model="ir.actions.act_window">
        <field name="name">Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_my_requests': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new request
            </p>
            <p>
                Create different types of requests that will go through an approval workflow.
            </p>
        </field>
    </record>
    
    <menuitem id="menu_bssic_requests" 
              name="My Requests" 
              parent="menu_bssic_requests_root" 
              action="action_bssic_requests" 
              sequence="10"/>
    
    <!-- Team Requests Menu (for managers) -->
    <record id="action_bssic_team_requests" model="ir.actions.act_window">
        <field name="name">Team Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_my_team_requests': 1}</field>
    </record>
    
    <menuitem id="menu_bssic_team_requests" 
              name="Team Requests" 
              parent="menu_bssic_requests_root" 
              action="action_bssic_team_requests" 
              sequence="20" 
              groups="bssic_requests.group_bssic_direct_manager"/>
    
    <!-- Pending Approval Menus -->
    <record id="action_bssic_direct_manager_approval" model="ir.actions.act_window">
        <field name="name">Direct Manager Approvals</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'direct_manager')]</field>
        <field name="context">{'search_default_my_team_requests': 1}</field>
    </record>
    
    <menuitem id="menu_bssic_direct_manager_approval" 
              name="Pending Direct Manager Approval" 
              parent="menu_bssic_requests_root" 
              action="action_bssic_direct_manager_approval" 
              sequence="30" 
              groups="bssic_requests.group_bssic_direct_manager"/>
    
    <record id="action_bssic_audit_manager_approval" model="ir.actions.act_window">
        <field name="name">Audit Manager Approvals</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'audit_manager')]</field>
    </record>
    
    <menuitem id="menu_bssic_audit_manager_approval" 
              name="Pending Audit Manager Approval" 
              parent="menu_bssic_requests_root" 
              action="action_bssic_audit_manager_approval" 
              sequence="40" 
              groups="bssic_requests.group_bssic_audit_manager"/>
    
    <record id="action_bssic_it_manager_approval" model="ir.actions.act_window">
        <field name="name">IT Manager Approvals</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'it_manager')]</field>
    </record>
    
    <menuitem id="menu_bssic_it_manager_approval" 
              name="Pending IT Manager Approval" 
              parent="menu_bssic_requests_root" 
              action="action_bssic_it_manager_approval" 
              sequence="50" 
              groups="bssic_requests.group_bssic_it_manager"/>
    
    <!-- IT Staff Assigned Requests -->
    <record id="action_bssic_assigned_requests" model="ir.actions.act_window">
        <field name="name">Assigned Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_assigned_to_me': 1}</field>
        <field name="domain">[('state', 'in', ['in_progress'])]</field>
    </record>
    
    <menuitem id="menu_bssic_assigned_requests" 
              name="Assigned Requests" 
              parent="menu_bssic_requests_root" 
              action="action_bssic_assigned_requests" 
              sequence="60" 
              groups="bssic_requests.group_bssic_it_staff"/>
    
    <!-- All Requests (for IT Manager) -->
    <record id="action_bssic_all_requests" model="ir.actions.act_window">
        <field name="name">All Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
    </record>
    
    <menuitem id="menu_bssic_all_requests" 
              name="All Requests" 
              parent="menu_bssic_requests_root" 
              action="action_bssic_all_requests" 
              sequence="70" 
              groups="bssic_requests.group_bssic_it_manager"/>
    
    <!-- Request Types Menu -->
    <record id="action_bssic_request_types" model="ir.actions.act_window">
        <field name="name">Request Types</field>
        <field name="res_model">bssic.request.type</field>
        <field name="view_mode">tree,form</field>
    </record>
    
    <menuitem id="menu_bssic_configuration" 
              name="Configuration" 
              parent="menu_bssic_requests_root" 
              sequence="100" 
              groups="bssic_requests.group_bssic_it_manager"/>
    
    <menuitem id="menu_bssic_request_types" 
              name="Request Types" 
              parent="menu_bssic_configuration" 
              action="action_bssic_request_types" 
              sequence="10" 
              groups="bssic_requests.group_bssic_it_manager"/>
</odoo>