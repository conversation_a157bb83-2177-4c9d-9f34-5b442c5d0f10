from odoo import models, fields, api, _
from odoo.exceptions import UserError
from lxml import etree


class StationeryItem(models.Model):
    _name = 'bssic.stationery.item'
    _description = 'Stationery Item'
    _order = 'sequence, id'

    name = fields.Char('Item Name', required=True)
    sequence = fields.Integer('Sequence', default=10)
    active = fields.Boolean('Active', default=True)


class StationeryRequest(models.Model):
    _name = 'bssic.stationery.request'
    _description = 'Stationery Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    # Basic Information
    name = fields.Char('Request Reference', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                   tracking=True, ondelete="restrict")
    employee_number = fields.Char(string='Employee Number (ID)', tracking=True,
                                help="Enter employee ID number to automatically fetch employee details")
    department_id = fields.Many2one(related='employee_id.department_id',
                                   string='Department', store=True, readonly=True)
    job_id = fields.Many2one(related='employee_id.job_id', string='Job Position',
                            store=True, readonly=True)
    request_date = fields.Date('Request Date', default=fields.Date.context_today,
                              required=True, tracking=True)

    # State and Lines
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager', 'Direct Manager Approval'),
        ('warehouse_approval', 'Warehouse Manager Approval'),
        ('hr_approval', 'HR Approval'),
        ('pending_receipt', 'Pending Receipt Confirmation'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    line_ids = fields.One2many('bssic.stationery.request.line', 'request_id', string='Stationery Items')

    # Approval Information
    direct_manager_id = fields.Many2one('hr.employee', string='Direct Manager',
                                       related='employee_id.parent_id', store=True, readonly=True)
    warehouse_manager_id = fields.Many2one('hr.employee', string='Warehouse Manager', tracking=True)
    hr_manager_id = fields.Many2one('hr.employee', string='HR Manager', tracking=True)
    rejection_reason = fields.Text('Rejection Reason', tracking=True)
    notes = fields.Text('Notes', tracking=True)

    # Approval Logs
    submission_user_id = fields.Many2one('res.users', string='Submitted by', readonly=True, tracking=True)
    submission_date = fields.Datetime(string='Submission Date', readonly=True, tracking=True)
    direct_manager_approval_user_id = fields.Many2one('res.users', string='Approved by (Manager)', readonly=True, tracking=True)
    direct_manager_approval_date = fields.Datetime(string='Manager Approval Date', readonly=True, tracking=True)
    warehouse_approval_user_id = fields.Many2one('res.users', string='Approved by (Warehouse)', readonly=True, tracking=True)
    warehouse_approval_date = fields.Datetime(string='Warehouse Approval Date', readonly=True, tracking=True)
    hr_approval_user_id = fields.Many2one('res.users', string='Approved by (HR)', readonly=True, tracking=True)
    hr_approval_date = fields.Datetime(string='HR Approval Date', readonly=True, tracking=True)
    receipt_confirmation_user_id = fields.Many2one('res.users', string='Receipt Confirmed by', readonly=True, tracking=True)
    receipt_confirmation_date = fields.Datetime(string='Receipt Confirmation Date', readonly=True, tracking=True)
    receipt_notes = fields.Text('Receipt Notes', tracking=True)
    rejection_user_id = fields.Many2one('res.users', string='Rejected by', readonly=True, tracking=True)
    rejection_date = fields.Datetime(string='Rejection Date', readonly=True, tracking=True)

    # Computed Fields
    is_manager = fields.Boolean(string='Is Manager', compute='_compute_is_manager', store=False)
    is_hr_manager = fields.Boolean(string='Is HR Manager', compute='_compute_is_hr_manager', store=False)
    is_warehouse_manager = fields.Boolean(string='Is Warehouse Manager', compute='_compute_is_warehouse_manager', store=False)

    # Activity Log
    activity_log_ids = fields.One2many('bssic.request.activity.log', 'stationery_request_id', string='Activity Log')

    @api.constrains('line_ids')
    def _check_line_ids(self):
        """Check that the request has at least one stationery item when saving"""
        for record in self:
            if record.state != 'draft' and not record.line_ids:
                raise UserError(_('You cannot save a request without any stationery items.'))

    @api.depends()
    def _compute_is_manager(self):
        """Check if the current user has direct manager permissions"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        for record in self:
            record.is_manager = is_manager

    @api.depends()
    def _compute_is_hr_manager(self):
        """Check if the current user has HR manager permissions"""
        is_hr_manager = self.env.user.has_group('bssic_requests.group_bssic_hr_manager')
        for record in self:
            record.is_hr_manager = is_hr_manager

    @api.depends()
    def _compute_is_warehouse_manager(self):
        """Check if the current user has Warehouse manager permissions"""
        is_warehouse_manager = self.env.user.has_group('bssic_requests.group_bssic_warehouse_manager')
        for record in self:
            record.is_warehouse_manager = is_warehouse_manager

    @api.model
    def default_get(self, fields_list):
        """Set default employee to current user's employee record"""
        res = super(StationeryRequest, self).default_get(fields_list)

        # Get current user's employee record
        current_user = self.env.user
        employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

        if employee:
            res['employee_id'] = employee.id
            # Also set employee_number if available
            if employee.int_id:
                res['employee_number'] = employee.int_id

        return res

    @api.model
    def create(self, vals):
        # Set sequence number
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('bssic.stationery.request') or _('New')
        return super(StationeryRequest, self).create(vals)

    def action_confirm_receipt_wizard(self):
        """Open the receipt confirmation wizard"""
        return {
            'name': _('Confirm Receipt'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.receipt.confirmation.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_stationery_request_id': self.id}
        }

    def action_reject(self):
        return {
            'name': _('Reject Request'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.request.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_request_id': self.id, 'default_model': 'bssic.stationery.request'}
        }

    # Add this method to fetch employee details based on employee number
    @api.onchange('employee_number')
    def _onchange_employee_number(self):
        # Check if user is a manager
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        current_user = self.env.user

        if self.employee_number:
            # Try with int_id field
            employee = self.env['hr.employee'].search([('int_id', '=', self.employee_number)], limit=1)

            if employee:
                # If user is a manager, check if the employee is in their department and reports to them
                if is_manager:
                    manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                    # Check if employee is the manager or reports to the manager in the same department
                    if (employee.id == manager_employee.id or
                        (employee.department_id.id == manager_employee.department_id.id and
                         employee.parent_id.id == manager_employee.id)):
                        self.employee_id = employee.id
                    else:
                        # Reset to manager's employee number
                        if manager_employee.int_id:
                            self.employee_number = manager_employee.int_id
                        self.employee_id = manager_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only select employees in your department who report to you.')
                        }}
                # If not a manager, only allow selecting themselves
                else:
                    user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                    if employee.id == user_employee.id:
                        self.employee_id = employee.id
                    else:
                        # Reset to user's employee number
                        if user_employee.int_id:
                            self.employee_number = user_employee.int_id
                        self.employee_id = user_employee.id
                        return {'warning': {
                            'title': _('Permission Error'),
                            'message': _('You can only create requests for yourself.')
                        }}
            else:
                # Clear the employee_id if no matching employee is found
                self.employee_id = False
                return {'warning': {
                    'title': _('Warning'),
                    'message': _('No employee found with this employee number.')
                }}

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        """Check permissions when employee_id is changed directly"""
        # Check if user is a manager
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        current_user = self.env.user

        if self.employee_id:
            # If user is a manager, check if the employee is in their department and reports to them
            if is_manager:
                manager_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                # Check if employee is the manager or reports to the manager in the same department
                if (self.employee_id.id == manager_employee.id or
                    (self.employee_id.department_id.id == manager_employee.department_id.id and
                     self.employee_id.parent_id.id == manager_employee.id)):
                    # Valid selection, update employee_number
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    # Invalid selection, reset to manager's employee
                    self.employee_id = manager_employee
                    if manager_employee.int_id:
                        self.employee_number = manager_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only select employees in your department who report to you.')
                    }}
            # If not a manager, only allow selecting themselves
            else:
                user_employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

                if self.employee_id.id == user_employee.id:
                    # Valid selection, update employee_number
                    if self.employee_id.int_id:
                        self.employee_number = self.employee_id.int_id
                else:
                    # Invalid selection, reset to current user's employee
                    self.employee_id = user_employee
                    if user_employee.int_id:
                        self.employee_number = user_employee.int_id
                    return {'warning': {
                        'title': _('Permission Error'),
                        'message': _('You can only create requests for yourself.')
                    }}
